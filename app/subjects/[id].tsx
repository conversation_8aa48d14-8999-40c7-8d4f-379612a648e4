import { router, useLocalSearchParams } from "expo-router";
import React from "react";
import { FlatList, SafeAreaView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { chapters, subjects } from "../../data/subjects";
import { Chapter } from "../../types";

export default function SubjectChapters() {
  const { id } = useLocalSearchParams<{ id: string }>();

  const subject = subjects.find((s) => s.id === id);
  const subjectChapters = chapters
    .filter((chapter) => chapter.subjectId === id)
    .sort((a, b) => a.order - b.order);

  const handleChapterPress = (chapter: Chapter) => {
    router.push(`/chapters/${chapter.id}`);
  };

  const renderChapter = ({ item }: { item: Chapter }) => (
    <TouchableOpacity style={styles.chapterCard} onPress={() => handleChapterPress(item)}>
      <View style={styles.chapterHeader}>
        <View style={[styles.chapterNumber, { backgroundColor: subject?.color }]}>
          <Text style={styles.chapterNumberText}>{item.order}</Text>
        </View>
        <View style={styles.chapterInfo}>
          <Text style={styles.chapterTitle}>{item.title}</Text>
          {item.estimatedReadTime && (
            <Text style={styles.readTime}>📖 {item.estimatedReadTime} min read</Text>
          )}
        </View>
        <Text style={styles.arrow}>→</Text>
      </View>
    </TouchableOpacity>
  );

  if (!subject) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Subject not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={[styles.header, { backgroundColor: subject.color }]}>
        <Text style={styles.subjectIcon}>{subject.icon}</Text>
        <Text style={styles.subjectName}>{subject.name}</Text>
        <Text style={styles.subjectDescription}>{subject.description}</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.chaptersTitle}>Chapters ({subjectChapters.length})</Text>

        <FlatList
          data={subjectChapters}
          renderItem={renderChapter}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: "center",
  },
  subjectIcon: {
    fontSize: 48,
    marginBottom: 12,
  },
  subjectName: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 8,
    textAlign: "center",
  },
  subjectDescription: {
    fontSize: 14,
    color: "#fff",
    textAlign: "center",
    opacity: 0.9,
  },
  content: {
    flex: 1,
    backgroundColor: "#f5f5f5",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20,
    paddingTop: 20,
  },
  chaptersTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#333",
    marginHorizontal: 20,
    marginBottom: 16,
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  chapterCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chapterHeader: {
    flexDirection: "row",
    alignItems: "center",
  },
  chapterNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  chapterNumberText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 14,
  },
  chapterInfo: {
    flex: 1,
  },
  chapterTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  readTime: {
    fontSize: 12,
    color: "#666",
  },
  arrow: {
    fontSize: 18,
    color: "#ccc",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    fontSize: 18,
    color: "#666",
  },
});

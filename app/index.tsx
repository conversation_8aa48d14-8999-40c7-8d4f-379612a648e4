import { router } from "expo-router";
import React from "react";
import { FlatList, SafeAreaView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { subjects } from "../data/subjects";
import { Subject } from "../types";

export default function Index() {
  const handleSubjectPress = (subject: Subject) => {
    router.push(`/subjects/${subject.id}`);
  };

  const renderSubject = ({ item }: { item: Subject }) => (
    <TouchableOpacity
      style={[styles.subjectCard, { borderLeftColor: item.color }]}
      onPress={() => handleSubjectPress(item)}>
      <View style={styles.subjectHeader}>
        <Text style={styles.subjectIcon}>{item.icon}</Text>
        <Text style={styles.subjectName}>{item.name}</Text>
      </View>
      <Text style={styles.subjectDescription}>{item.description}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Choose a Subject</Text>
        <Text style={styles.subtitle}>Select a subject to start learning</Text>
      </View>

      <FlatList
        data={subjects}
        renderItem={renderSubject}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    padding: 20,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
  },
  listContainer: {
    padding: 16,
  },
  subjectCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    borderLeftWidth: 4,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  subjectHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  subjectIcon: {
    fontSize: 32,
    marginRight: 12,
  },
  subjectName: {
    fontSize: 20,
    fontWeight: "600",
    color: "#333",
    flex: 1,
  },
  subjectDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
  },
});

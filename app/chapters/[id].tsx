import { router, useLocalSearchParams } from "expo-router";
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { chapters, quizzes, subjects } from "../../data/subjects";

export default function ChapterDetail() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [currentPageIndex, setCurrentPageIndex] = useState(0);

  const chapter = chapters.find((c) => c.id === id);
  const subject = chapter ? subjects.find((s) => s.id === chapter.subjectId) : null;
  const quiz = quizzes.find((q) => q.chapterId === id);

  const currentPage = chapter?.pages[currentPageIndex];
  const isLastPage = currentPageIndex === (chapter?.pages.length || 0) - 1;
  const isFirstPage = currentPageIndex === 0;

  const handleTakeQuiz = () => {
    if (quiz) {
      router.push(`/quiz/${quiz.id}`);
    } else {
      Alert.alert("Quiz Not Available", "No quiz is available for this chapter yet.");
    }
  };

  const handleNextPage = () => {
    if (isLastPage) {
      handleTakeQuiz();
    } else {
      setCurrentPageIndex(currentPageIndex + 1);
    }
  };

  const handlePreviousPage = () => {
    if (!isFirstPage) {
      setCurrentPageIndex(currentPageIndex - 1);
    }
  };

  const formatContent = (content: string) => {
    // Simple markdown-like formatting
    const lines = content.split("\n");
    const formattedLines: React.ReactElement[] = [];
    let inCodeBlock = false;
    let codeBlockLines: string[] = [];

    lines.forEach((line, index) => {
      // Handle code blocks
      if (line.startsWith("```")) {
        if (inCodeBlock) {
          // End of code block
          if (codeBlockLines.length > 0) {
            formattedLines.push(
              <View key={`code-${index}`} style={styles.codeBlock}>
                <Text style={styles.codeText}>{codeBlockLines.join("\n")}</Text>
              </View>
            );
          }
          codeBlockLines = [];
          inCodeBlock = false;
        } else {
          // Start of code block
          inCodeBlock = true;
        }
        return;
      }

      if (inCodeBlock) {
        codeBlockLines.push(line);
        return;
      }

      // Handle regular markdown
      if (line.startsWith("# ")) {
        formattedLines.push(
          <Text key={index} style={styles.heading1}>
            {line.substring(2).trim()}
          </Text>
        );
      } else if (line.startsWith("## ")) {
        formattedLines.push(
          <Text key={index} style={styles.heading2}>
            {line.substring(3).trim()}
          </Text>
        );
      } else if (line.startsWith("### ")) {
        formattedLines.push(
          <Text key={index} style={styles.heading3}>
            {line.substring(4).trim()}
          </Text>
        );
      } else if (line.startsWith("- ") || line.startsWith("* ")) {
        // Handle bold text in bullet points
        const bulletText = line.substring(2).trim();
        const renderBulletWithBold = (text: string) => {
          const parts = text.split(/(\*\*.*?\*\*)/g);
          return parts.map((part, partIndex) => {
            if (part.startsWith("**") && part.endsWith("**")) {
              return (
                <Text key={partIndex} style={styles.boldText}>
                  {part.slice(2, -2)}
                </Text>
              );
            }
            return part;
          });
        };

        formattedLines.push(
          <Text key={index} style={styles.bulletPoint}>
            • {renderBulletWithBold(bulletText)}
          </Text>
        );
      } else if (line.trim() === "") {
        formattedLines.push(<View key={index} style={styles.spacing} />);
      } else if (line.trim().length > 0) {
        // Handle bold text **text**
        const renderTextWithBold = (text: string) => {
          const parts = text.split(/(\*\*.*?\*\*)/g);
          return parts.map((part, partIndex) => {
            if (part.startsWith("**") && part.endsWith("**")) {
              return (
                <Text key={partIndex} style={styles.boldText}>
                  {part.slice(2, -2)}
                </Text>
              );
            }
            return part;
          });
        };

        formattedLines.push(
          <Text key={index} style={styles.paragraph}>
            {renderTextWithBold(line)}
          </Text>
        );
      }
    });

    return formattedLines;
  };

  if (!chapter || !currentPage) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Chapter not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        <View style={styles.header}>
          <Text style={styles.chapterTitle}>{chapter.title}</Text>
          <Text style={styles.pageTitle}>{currentPage.title}</Text>
          <View style={styles.progressContainer}>
            <Text style={styles.progressText}>
              Page {currentPageIndex + 1} of {chapter.pages.length}
            </Text>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  {
                    width: `${((currentPageIndex + 1) / chapter.pages.length) * 100}%`,
                    backgroundColor: subject?.color || "#3B82F6",
                  },
                ]}
              />
            </View>
          </View>
          {chapter.estimatedReadTime && (
            <Text style={styles.readTime}>📖 {chapter.estimatedReadTime} min read</Text>
          )}
        </View>

        <View style={styles.contentContainer}>{formatContent(currentPage.content)}</View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.navButton, styles.previousButton]}
          onPress={handlePreviousPage}
          disabled={isFirstPage}>
          <Text style={[styles.navButtonText, isFirstPage && styles.disabledButtonText]}>
            Previous
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.navButton,
            styles.nextButton,
            { backgroundColor: subject?.color || "#3B82F6" },
          ]}
          onPress={handleNextPage}>
          <Text style={styles.nextButtonText}>
            {isLastPage ? (quiz ? "Take Quiz" : "Complete") : "Next"}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingBottom: 100,
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  chapterTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  readTime: {
    fontSize: 14,
    color: "#666",
  },
  contentContainer: {
    padding: 20,
  },
  heading1: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#333",
    marginTop: 20,
    marginBottom: 12,
  },
  heading2: {
    fontSize: 20,
    fontWeight: "600",
    color: "#333",
    marginTop: 16,
    marginBottom: 10,
  },
  heading3: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginTop: 14,
    marginBottom: 8,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    color: "#444",
    marginBottom: 12,
  },
  boldText: {
    fontWeight: "bold",
    color: "#333",
  },
  bulletPoint: {
    fontSize: 16,
    lineHeight: 24,
    color: "#444",
    marginBottom: 8,
    marginLeft: 16,
  },
  spacing: {
    height: 12,
  },
  codeBlock: {
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    padding: 12,
    marginVertical: 8,
    borderLeftWidth: 4,
    borderLeftColor: "#e9ecef",
  },
  codeText: {
    fontFamily: "monospace",
    fontSize: 14,
    color: "#495057",
    lineHeight: 20,
  },
  footer: {
    flexDirection: "row",
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: "#E5E7EB",
    backgroundColor: "#FFFFFF",
    gap: 12,
  },
  navButton: {
    flex: 1,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: "center",
  },
  previousButton: {
    backgroundColor: "#F3F4F6",
    borderWidth: 1,
    borderColor: "#D1D5DB",
  },
  nextButton: {
    backgroundColor: "#3B82F6",
  },
  navButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#374151",
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  disabledButtonText: {
    color: "#9CA3AF",
  },
  pageTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#374151",
    marginTop: 8,
    marginBottom: 12,
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressText: {
    fontSize: 14,
    color: "#6B7280",
    marginBottom: 8,
    textAlign: "center",
  },
  progressBar: {
    height: 4,
    backgroundColor: "#E5E7EB",
    borderRadius: 2,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#3B82F6",
    borderRadius: 2,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    fontSize: 18,
    color: "#666",
  },
});

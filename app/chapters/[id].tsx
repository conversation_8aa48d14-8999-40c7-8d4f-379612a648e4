import { router, useLocalSearchParams } from "expo-router";
import React from "react";
import {
  <PERSON><PERSON>,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { chapters, quizzes, subjects } from "../../data/subjects";

export default function ChapterDetail() {
  const { id } = useLocalSearchParams<{ id: string }>();

  const chapter = chapters.find((c) => c.id === id);
  const subject = chapter ? subjects.find((s) => s.id === chapter.subjectId) : null;
  const quiz = quizzes.find((q) => q.chapterId === id);

  const handleTakeQuiz = () => {
    if (quiz) {
      router.push(`/quiz/${quiz.id}`);
    } else {
      Alert.alert("Quiz Not Available", "No quiz is available for this chapter yet.");
    }
  };

  const formatContent = (content: string) => {
    // Simple markdown-like formatting
    const lines = content.split("\n");
    const formattedLines: React.ReactElement[] = [];
    let inCodeBlock = false;
    let codeBlockLines: string[] = [];

    lines.forEach((line, index) => {
      // Handle code blocks
      if (line.startsWith("```")) {
        if (inCodeBlock) {
          // End of code block
          if (codeBlockLines.length > 0) {
            formattedLines.push(
              <View key={`code-${index}`} style={styles.codeBlock}>
                <Text style={styles.codeText}>{codeBlockLines.join("\n")}</Text>
              </View>
            );
          }
          codeBlockLines = [];
          inCodeBlock = false;
        } else {
          // Start of code block
          inCodeBlock = true;
        }
        return;
      }

      if (inCodeBlock) {
        codeBlockLines.push(line);
        return;
      }

      // Handle regular markdown
      if (line.startsWith("# ")) {
        formattedLines.push(
          <Text key={index} style={styles.heading1}>
            {line.substring(2).trim()}
          </Text>
        );
      } else if (line.startsWith("## ")) {
        formattedLines.push(
          <Text key={index} style={styles.heading2}>
            {line.substring(3).trim()}
          </Text>
        );
      } else if (line.startsWith("### ")) {
        formattedLines.push(
          <Text key={index} style={styles.heading3}>
            {line.substring(4).trim()}
          </Text>
        );
      } else if (line.startsWith("- ") || line.startsWith("* ")) {
        formattedLines.push(
          <Text key={index} style={styles.bulletPoint}>
            • {line.substring(2).trim()}
          </Text>
        );
      } else if (line.trim() === "") {
        formattedLines.push(<View key={index} style={styles.spacing} />);
      } else if (line.trim().length > 0) {
        // Handle bold text **text** - for now just remove the asterisks
        // React Native Text nesting can be complex, so we'll use a simpler approach
        const cleanLine = line.replace(/\*\*(.*?)\*\*/g, "$1");

        formattedLines.push(
          <Text key={index} style={styles.paragraph}>
            {cleanLine}
          </Text>
        );
      }
    });

    return formattedLines;
  };

  if (!chapter) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Chapter not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        <View style={styles.header}>
          <Text style={styles.chapterTitle}>{chapter.title}</Text>
          {chapter.estimatedReadTime && (
            <Text style={styles.readTime}>📖 {chapter.estimatedReadTime} min read</Text>
          )}
        </View>

        <View style={styles.contentContainer}>{formatContent(chapter.content)}</View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.quizButton, { backgroundColor: subject?.color || "#3B82F6" }]}
          onPress={handleTakeQuiz}>
          <Text style={styles.quizButtonText}>{quiz ? "Take Quiz" : "Quiz Coming Soon"}</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingBottom: 100,
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  chapterTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  readTime: {
    fontSize: 14,
    color: "#666",
  },
  contentContainer: {
    padding: 20,
  },
  heading1: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#333",
    marginTop: 20,
    marginBottom: 12,
  },
  heading2: {
    fontSize: 20,
    fontWeight: "600",
    color: "#333",
    marginTop: 16,
    marginBottom: 10,
  },
  heading3: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginTop: 14,
    marginBottom: 8,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    color: "#444",
    marginBottom: 12,
  },
  boldText: {
    fontWeight: "bold",
    color: "#333",
  },
  bulletPoint: {
    fontSize: 16,
    lineHeight: 24,
    color: "#444",
    marginBottom: 8,
    marginLeft: 16,
  },
  spacing: {
    height: 12,
  },
  codeBlock: {
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    padding: 12,
    marginVertical: 8,
    borderLeftWidth: 4,
    borderLeftColor: "#e9ecef",
  },
  codeText: {
    fontFamily: "monospace",
    fontSize: 14,
    color: "#495057",
    lineHeight: 20,
  },
  footer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#e0e0e0",
  },
  quizButton: {
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
  },
  quizButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    fontSize: 18,
    color: "#666",
  },
});

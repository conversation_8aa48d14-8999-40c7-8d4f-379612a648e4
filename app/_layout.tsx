import { DarkTheme, <PERSON><PERSON><PERSON><PERSON>heme, ThemeProvider } from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import "react-native-reanimated";

import { useColorScheme } from "@/hooks/useColorScheme";

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ThemeProvider value={colorScheme === "dark" ? DarkTheme : DefaultTheme}>
      <Stack>
        <Stack.Screen name="index" options={{ title: "Learning App" }} />
        <Stack.Screen name="subjects/[id]" options={{ title: "Chapters" }} />
        <Stack.Screen name="chapters/[id]" options={{ title: "Chapter" }} />
        <Stack.Screen name="quiz/[id]" options={{ title: "Quiz" }} />
        <Stack.Screen name="quiz/result/[id]" options={{ title: "Quiz Results" }} />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
    </ThemeProvider>
  );
}

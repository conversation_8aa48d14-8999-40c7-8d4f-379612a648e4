import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, ScrollView } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { quizzes, chapters, subjects } from '../../../data/subjects';

export default function QuizResult() {
  const { id, score, correctAnswers, totalQuestions, answers } = useLocalSearchParams<{
    id: string;
    score: string;
    correctAnswers: string;
    totalQuestions: string;
    answers: string;
  }>();
  
  const quiz = quizzes.find(q => q.id === id);
  const chapter = quiz ? chapters.find(c => c.id === quiz.chapterId) : null;
  const subject = chapter ? subjects.find(s => s.id === chapter.subjectId) : null;
  
  const scoreNum = parseInt(score || '0');
  const correctNum = parseInt(correctAnswers || '0');
  const totalNum = parseInt(totalQuestions || '0');
  const userAnswers = answers ? JSON.parse(answers) : [];
  
  const passed = quiz?.passingScore ? scoreNum >= quiz.passingScore : scoreNum >= 70;

  const getScoreColor = () => {
    if (scoreNum >= 80) return '#10B981'; // Green
    if (scoreNum >= 60) return '#F59E0B'; // Yellow
    return '#EF4444'; // Red
  };

  const getScoreMessage = () => {
    if (scoreNum >= 90) return 'Excellent! 🎉';
    if (scoreNum >= 80) return 'Great job! 👏';
    if (scoreNum >= 70) return 'Good work! 👍';
    if (scoreNum >= 60) return 'Not bad! 📚';
    return 'Keep studying! 💪';
  };

  const handleRetakeQuiz = () => {
    router.replace(`/quiz/${id}`);
  };

  const handleBackToChapter = () => {
    if (chapter) {
      router.replace(`/chapters/${chapter.id}`);
    }
  };

  const handleBackToSubject = () => {
    if (subject) {
      router.replace(`/subjects/${subject.id}`);
    }
  };

  if (!quiz) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Quiz results not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>Quiz Complete!</Text>
          <Text style={styles.subtitle}>{quiz.title}</Text>
        </View>

        <View style={styles.scoreContainer}>
          <View style={[styles.scoreCircle, { borderColor: getScoreColor() }]}>
            <Text style={[styles.scoreText, { color: getScoreColor() }]}>
              {scoreNum}%
            </Text>
          </View>
          <Text style={styles.scoreMessage}>{getScoreMessage()}</Text>
          <Text style={styles.scoreDetails}>
            {correctNum} out of {totalNum} questions correct
          </Text>
        </View>

        <View style={styles.statusContainer}>
          <View style={[styles.statusBadge, { backgroundColor: passed ? '#10B981' : '#EF4444' }]}>
            <Text style={styles.statusText}>
              {passed ? '✓ Passed' : '✗ Failed'}
            </Text>
          </View>
          {quiz.passingScore && (
            <Text style={styles.passingScore}>
              Passing score: {quiz.passingScore}%
            </Text>
          )}
        </View>

        <View style={styles.reviewContainer}>
          <Text style={styles.reviewTitle}>Review Answers</Text>
          {quiz.questions.map((question, index) => {
            const userAnswer = userAnswers[index];
            const isCorrect = userAnswer === question.correctAnswer;
            
            return (
              <View key={question.id} style={styles.questionReview}>
                <Text style={styles.questionNumber}>Question {index + 1}</Text>
                <Text style={styles.questionText}>{question.question}</Text>
                
                <View style={styles.answerReview}>
                  <Text style={[styles.answerLabel, { color: isCorrect ? '#10B981' : '#EF4444' }]}>
                    Your answer: {isCorrect ? '✓' : '✗'}
                  </Text>
                  <Text style={styles.answerText}>
                    {String.fromCharCode(65 + userAnswer)}. {question.options[userAnswer]}
                  </Text>
                  
                  {!isCorrect && (
                    <>
                      <Text style={[styles.answerLabel, { color: '#10B981' }]}>
                        Correct answer: ✓
                      </Text>
                      <Text style={styles.answerText}>
                        {String.fromCharCode(65 + question.correctAnswer)}. {question.options[question.correctAnswer]}
                      </Text>
                    </>
                  )}
                  
                  {question.explanation && (
                    <Text style={styles.explanation}>
                      💡 {question.explanation}
                    </Text>
                  )}
                </View>
              </View>
            );
          })}
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={handleRetakeQuiz}
        >
          <Text style={styles.secondaryButtonText}>Retake Quiz</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.button, styles.primaryButton, { backgroundColor: subject?.color || '#3B82F6' }]}
          onPress={handleBackToChapter}
        >
          <Text style={styles.primaryButtonText}>Back to Chapter</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    paddingBottom: 100,
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  scoreContainer: {
    backgroundColor: '#fff',
    margin: 20,
    padding: 30,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  scoreCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  scoreText: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  scoreMessage: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  scoreDetails: {
    fontSize: 14,
    color: '#666',
  },
  statusContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  statusBadge: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 8,
  },
  statusText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  passingScore: {
    fontSize: 12,
    color: '#666',
  },
  reviewContainer: {
    margin: 20,
  },
  reviewTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  questionReview: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  questionNumber: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666',
    marginBottom: 4,
  },
  questionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 12,
  },
  answerReview: {
    gap: 4,
  },
  answerLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 8,
  },
  answerText: {
    fontSize: 14,
    color: '#333',
  },
  explanation: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 8,
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: 20,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 12,
  },
  button: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#3B82F6',
  },
  secondaryButton: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 18,
    color: '#666',
  },
});

export interface Subject {
  id: string;
  name: string;
  description: string;
  icon?: string;
  color?: string;
}

export interface Chapter {
  id: string;
  subjectId: string;
  title: string;
  content: string;
  order: number;
  estimatedReadTime?: number; // in minutes
}

export interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number; // index of correct option
  explanation?: string;
}

export interface Quiz {
  id: string;
  chapterId: string;
  title: string;
  description?: string;
  questions: QuizQuestion[];
  passingScore?: number; // percentage
}

export interface QuizResult {
  quizId: string;
  chapterId: string;
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  answers: number[]; // user's selected answers
  completedAt: Date;
}

export interface UserProgress {
  subjectId: string;
  completedChapters: string[];
  quizResults: QuizResult[];
}

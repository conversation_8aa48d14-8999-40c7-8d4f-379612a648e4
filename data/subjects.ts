import { Chapter, Quiz, Subject } from "../types";

export const subjects: Subject[] = [
  {
    id: "computer-science",
    name: "Computer Science",
    description:
      "Learn fundamental concepts of computer science including programming, algorithms, and data structures.",
    icon: "💻",
    color: "#3B82F6",
  },
  {
    id: "mathematics",
    name: "Mathematics",
    description: "Explore mathematical concepts from basic arithmetic to advanced calculus.",
    icon: "📐",
    color: "#10B981",
  },
  {
    id: "physics",
    name: "Physics",
    description: "Understand the fundamental laws that govern the universe.",
    icon: "⚛️",
    color: "#8B5CF6",
  },
];

export const chapters: Chapter[] = [
  {
    id: "cs-intro",
    subjectId: "computer-science",
    title: "Introduction to Computer Science",
    order: 1,
    estimatedReadTime: 15,
    pages: [
      {
        id: "cs-intro-page-1",
        title: "What is Computer Science?",
        order: 1,
        content: `# Introduction to Computer Science

Computer Science is the study of computational systems and the design of computer systems and their applications. It encompasses both the theoretical foundations of computing and practical techniques for their implementation and application.

## What is Computer Science?

Computer Science combines mathematical rigor with engineering pragmatism. It involves:

- **Problem Solving**: Breaking down complex problems into manageable parts
- **Algorithm Design**: Creating step-by-step procedures to solve problems
- **Programming**: Implementing solutions using programming languages
- **System Design**: Building efficient and scalable computer systems`,
      },
      {
        id: "cs-intro-page-2",
        title: "Key Areas of Computer Science",
        order: 2,
        content: `## Key Areas of Computer Science

### 1. Programming and Software Development
Learning to write code in various programming languages to create applications, websites, and systems.

### 2. Algorithms and Data Structures
Understanding efficient ways to organize data and solve computational problems.

### 3. Computer Systems and Architecture
Learning how computers work at the hardware and software level.

### 4. Database Systems
Managing and organizing large amounts of data efficiently.

### 5. Artificial Intelligence
Creating systems that can perform tasks that typically require human intelligence.`,
      },
      {
        id: "cs-intro-page-3",
        title: "Career Opportunities",
        order: 3,
        content: `## Why Study Computer Science?

Computer Science offers numerous career opportunities in our increasingly digital world:
- Software Development
- Data Science
- Cybersecurity
- Web Development
- Game Development
- Research and Academia

The field continues to evolve rapidly, making it an exciting and dynamic area of study.

### Getting Started

To begin your journey in Computer Science, you'll need to develop:
- **Logical thinking** and problem-solving skills
- **Mathematical foundation** for understanding algorithms
- **Programming skills** in at least one language
- **Curiosity** about how technology works

Ready to dive deeper? Let's explore programming fundamentals next!`,
      },
    ],
  },
  {
    id: "cs-programming-basics",
    subjectId: "computer-science",
    title: "Programming Fundamentals",
    order: 2,
    estimatedReadTime: 20,
    pages: [
      {
        id: "cs-programming-page-1",
        title: "What is Programming?",
        order: 1,
        content: `# Programming Fundamentals

Programming is the process of creating instructions for computers to follow. These instructions are written in programming languages that computers can understand and execute.

## What is Programming?

Programming involves:
- **Writing Code**: Creating instructions using a programming language
- **Problem Solving**: Breaking down problems into logical steps
- **Debugging**: Finding and fixing errors in code
- **Testing**: Ensuring the program works correctly

Programming is like giving directions to a computer. Just as you would give step-by-step directions to a friend, you give step-by-step instructions to a computer through code.`,
      },
      {
        id: "cs-programming-page-2",
        title: "Variables and Data Types",
        order: 2,
        content: `## Basic Programming Concepts

### Variables
Variables are containers that store data values. They have names and can hold different types of data.

Example:
\`\`\`
name = "Alice"
age = 25
height = 5.6
\`\`\`

### Data Types
Common data types include:
- **Integers**: Whole numbers (1, 2, 3)
- **Strings**: Text ("Hello", "World")
- **Booleans**: True or False values
- **Floats**: Decimal numbers (3.14, 2.5)

Think of variables as labeled boxes where you can store different types of information. The label is the variable name, and the contents are the data.`,
      },
    ],
  },
  {
    id: "cs-algorithms",
    subjectId: "computer-science",
    title: "Introduction to Algorithms",
    order: 3,
    estimatedReadTime: 25,
    pages: [
      {
        id: "cs-algorithms-page-1",
        title: "What is an Algorithm?",
        order: 1,
        content: `# Introduction to Algorithms

An algorithm is a step-by-step procedure for solving a problem or completing a task. In computer science, algorithms are fundamental to programming and problem-solving.

## What is an Algorithm?

An algorithm is:
- A finite sequence of well-defined instructions
- A method for solving a problem
- Independent of any programming language
- Must terminate after a finite number of steps

## Characteristics of Good Algorithms

### 1. Correctness
The algorithm should produce the correct output for all valid inputs.

### 2. Efficiency
The algorithm should use resources (time and space) efficiently.

### 3. Clarity
The algorithm should be easy to understand and implement.

### 4. Generality
The algorithm should work for a wide range of inputs.

## Algorithm Examples

### Example 1: Finding the Maximum Number
**Problem**: Find the largest number in a list of numbers.

**Algorithm**:
1. Start with the first number as the maximum
2. Compare each remaining number with the current maximum
3. If a number is larger, make it the new maximum
4. Continue until all numbers are checked
5. Return the maximum

### Example 2: Linear Search
**Problem**: Find if a specific value exists in a list.

**Algorithm**:
1. Start at the beginning of the list
2. Compare each element with the target value
3. If found, return the position
4. If not found after checking all elements, return "not found"

## Algorithm Analysis

### Time Complexity
Measures how the running time of an algorithm grows with input size.

Common time complexities:
- **O(1)**: Constant time
- **O(n)**: Linear time
- **O(n²)**: Quadratic time
- **O(log n)**: Logarithmic time

### Space Complexity
Measures how much memory an algorithm uses relative to input size.

## Types of Algorithms

### 1. Sorting Algorithms
Arrange data in a specific order (e.g., Bubble Sort, Quick Sort)

### 2. Searching Algorithms
Find specific elements in data structures (e.g., Binary Search)

### 3. Graph Algorithms
Work with graph data structures (e.g., shortest path algorithms)

### 4. Dynamic Programming
Solve complex problems by breaking them into simpler subproblems

## Pseudocode

Pseudocode is a way to describe algorithms using simple, human-readable language:

\`\`\`
ALGORITHM FindMax(numbers)
    max = numbers[0]
    FOR each number in numbers
        IF number > max THEN
            max = number
        END IF
    END FOR
    RETURN max
\`\`\`

Understanding algorithms is crucial for becoming an effective programmer and problem solver in computer science.`,
      },
    ],
  },
];

export const quizzes: Quiz[] = [
  {
    id: "quiz-cs-intro",
    chapterId: "cs-intro",
    title: "Introduction to Computer Science Quiz",
    description: "Test your understanding of basic computer science concepts.",
    passingScore: 70,
    questions: [
      {
        id: "q1",
        question: "What is Computer Science primarily concerned with?",
        options: [
          "Only hardware design",
          "Computational systems and their applications",
          "Only software development",
          "Only mathematical theories",
        ],
        correctAnswer: 1,
        explanation:
          "Computer Science encompasses both theoretical foundations and practical applications of computational systems.",
      },
      {
        id: "q2",
        question: "Which of the following is NOT a key area of Computer Science?",
        options: [
          "Algorithm Design",
          "Database Systems",
          "Cooking Techniques",
          "Artificial Intelligence",
        ],
        correctAnswer: 2,
        explanation:
          "Cooking techniques are not related to Computer Science, while the other options are all key areas of the field.",
      },
      {
        id: "q3",
        question: "What does problem solving in Computer Science involve?",
        options: [
          "Only writing code",
          "Breaking down complex problems into manageable parts",
          "Only using existing solutions",
          "Avoiding difficult problems",
        ],
        correctAnswer: 1,
        explanation:
          "Problem solving in Computer Science involves breaking down complex problems into smaller, manageable parts that can be solved systematically.",
      },
    ],
  },
  {
    id: "quiz-programming-basics",
    chapterId: "cs-programming-basics",
    title: "Programming Fundamentals Quiz",
    description: "Test your knowledge of basic programming concepts.",
    passingScore: 70,
    questions: [
      {
        id: "q1",
        question: "What is a variable in programming?",
        options: [
          "A type of loop",
          "A container that stores data values",
          "A programming language",
          "A type of error",
        ],
        correctAnswer: 1,
        explanation:
          "A variable is a container that stores data values and has a name that can be used to reference the stored data.",
      },
      {
        id: "q2",
        question: "Which of the following is a Boolean data type value?",
        options: ["42", '"Hello"', "True", "3.14"],
        correctAnswer: 2,
        explanation: "Boolean data types can only have two values: True or False.",
      },
      {
        id: "q3",
        question: "What is the purpose of a function in programming?",
        options: [
          "To store data",
          "To create reusable blocks of code",
          "To define variables",
          "To handle errors",
        ],
        correctAnswer: 1,
        explanation:
          "Functions are reusable blocks of code that perform specific tasks and can be called multiple times throughout a program.",
      },
      {
        id: "q4",
        question: "Which programming language is mentioned as great for beginners?",
        options: ["C++", "JavaScript", "Python", "Java"],
        correctAnswer: 2,
        explanation:
          "Python is mentioned as being great for beginners due to its simple syntax and readability.",
      },
    ],
  },
  {
    id: "quiz-algorithms",
    chapterId: "cs-algorithms",
    title: "Introduction to Algorithms Quiz",
    description: "Test your understanding of algorithms and their characteristics.",
    passingScore: 70,
    questions: [
      {
        id: "q1",
        question: "What is an algorithm?",
        options: [
          "A programming language",
          "A step-by-step procedure for solving a problem",
          "A type of computer",
          "A software application",
        ],
        correctAnswer: 1,
        explanation:
          "An algorithm is a finite sequence of well-defined instructions that provides a step-by-step procedure for solving a problem.",
      },
      {
        id: "q2",
        question: "Which is NOT a characteristic of a good algorithm?",
        options: ["Correctness", "Efficiency", "Complexity", "Clarity"],
        correctAnswer: 2,
        explanation:
          "Complexity is not a desirable characteristic. Good algorithms should be correct, efficient, clear, and general.",
      },
      {
        id: "q3",
        question: "What does O(n) represent in algorithm analysis?",
        options: [
          "Constant time complexity",
          "Linear time complexity",
          "Quadratic time complexity",
          "Logarithmic time complexity",
        ],
        correctAnswer: 1,
        explanation:
          "O(n) represents linear time complexity, meaning the running time grows linearly with the input size.",
      },
      {
        id: "q4",
        question: "In the linear search algorithm, what happens if the target value is not found?",
        options: [
          "The algorithm crashes",
          "It returns the first element",
          'It returns "not found"',
          "It continues searching indefinitely",
        ],
        correctAnswer: 2,
        explanation:
          'If the target value is not found after checking all elements, the linear search algorithm returns "not found".',
      },
    ],
  },
];
